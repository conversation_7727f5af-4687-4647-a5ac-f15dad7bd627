/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
using System;
using System.Data;
using System.Threading;
using System.Globalization;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using SplendidCRM._controls;
using System.Collections.Generic;
using SplendidCRM.TGSAP;

namespace SplendidCRM
{
	/// <summary>
	/// Summary description for SplendidPage.
	/// </summary>
	public class SplendidPagePassword : System.Web.UI.Page
	{
		protected bool     bDebug = false;
		// 08/29/2005 Paul.  Only store the absolute minimum amount of data.  
		// This means remove the data that is accessable from the Security object. 
		// The security data is not accessed frequently enough to justify initialization in every user control. 
		// High frequency objects are L10N and TimeZone. 
		protected string   m_sCULTURE    ;
		protected string   m_sDATEFORMAT ;
		protected string   m_sTIMEFORMAT ;
		protected Guid     m_gTIMEZONE   ;
		protected bool     m_bPrintView  = false;
		protected bool     m_bIsAdminPage = false;

		// L10n is an abbreviation for Localization (between the L & n are 10 characters. 
		protected L10N     L10n          ;  // 08/28/2005 Paul.  Keep old L10n name, and rename the object to simplify updated approach. 
		protected TimeZone T10n          ;
		protected Currency C10n          ;

        public SplendidPagePassword()
		{
			this.PreInit += new EventHandler(Page_PreInit);
		}

		public bool PrintView
		{
			get
			{
				return m_bPrintView;
			}
			set
			{
				m_bPrintView = value;
				Context.Items["PrintView"] = m_bPrintView;
			}
		}

		public bool IsAdminPage
		{
			get
			{
				return m_bIsAdminPage;
			}
			set
			{
				m_bIsAdminPage = value;
			}
		}

		public void SetMenu(string sMODULE)
		{
			// 01/20/2007 Paul.  Move the menu code to a fuction so that will only get called in EditView, DetailView and ListView controls. 
			// 01/19/2007 Paul.  If a MasterPage is in use, then we need to set the ActiveTab. 
			if ( !String.IsNullOrEmpty(sMODULE) )
			{
				if ( Master != null )
				{
					SplendidCRM.Themes.Sugar.TabMenu ctlTabMenu = Master.FindControl("ctlTabMenu") as SplendidCRM.Themes.Sugar.TabMenu;
					if ( ctlTabMenu != null )
					{
						// 01/20/2007 Paul.  Only set the first control as each 
						// SplendidControl on page will pass through this code. 
						if ( String.IsNullOrEmpty(ctlTabMenu.ActiveTab) )
							ctlTabMenu.ActiveTab = sMODULE;
					}
				}
			}
		}

		public void SetPageTitle(string sTitle)
		{
			// 01/20/2007 Paul.  Wrap the page title function to minimized differences between Web1.2.
			Page.Title = sTitle;
		}

		// 03/07/2008 Paul.  There is a better time to initialize the culture. 
		protected override void InitializeCulture()
		{
			// 08/30/2005 Paul.  Move the L10N creation to this get function so that the first control 
			// that gets created will cause the creation of L10N.  The UserControls get the OnInit event before the Page onInit event. 
			// 03/07/2008 Paul.  The page lifecycle has been designed to always call InitializeCulture before the page itself 
			// or any of its child controls have done any work with localized resources.
			m_sCULTURE     = Sql.ToString (Session["USER_SETTINGS/CULTURE"   ]);
			m_sDATEFORMAT  = Sql.ToString (Session["USER_SETTINGS/DATEFORMAT"]);
			m_sTIMEFORMAT  = Sql.ToString (Session["USER_SETTINGS/TIMEFORMAT"]);

			L10n = new L10N(m_sCULTURE);
			// 03/07/2008 Paul.  We need to set the page culture so that the AJAX engine will initialize Sys.CultureInfo.CurrentCulture. 
			this.Culture = L10n.NAME;

			// 08/30/2005 Paul.  Move the TimeZone creation to this get function so that the first control 
			// that gets created will cause the creation of TimeZone.  The UserControls get the OnInit event before the Page onInit event. 
			m_gTIMEZONE = Sql.ToGuid(Session["USER_SETTINGS/TIMEZONE"]);
			T10n = TimeZone.CreateTimeZone(m_gTIMEZONE);
			if ( T10n.ID != m_gTIMEZONE )
			{
				// 08/30/2005 Paul. If we are using a default, then update the session so that future controls will be quicker. 
				m_gTIMEZONE = T10n.ID ;
				Session["USER_SETTINGS/TIMEZONE"] = m_gTIMEZONE.ToString() ;
			}

			Guid gCURRENCY_ID = Sql.ToGuid(Session["USER_SETTINGS/CURRENCY"]);
			C10n = Currency.CreateCurrency(gCURRENCY_ID);
			if ( C10n.ID != gCURRENCY_ID )
			{
				// 05/09/2006 Paul. If we are using a default, then update the session so that future controls will be quicker. 
				gCURRENCY_ID = C10n.ID;
				Session["USER_SETTINGS/CURRENCY"] = gCURRENCY_ID.ToString();
				
			}
			// 08/05/2006 Paul.  We cannot set the CurrencyDecimalSeparator directly on Mono as it is read-only.  
			// Hold off setting the CurrentCulture until we have updated all the settings. 
			CultureInfo culture = CultureInfo.CreateSpecificCulture(L10n.NAME);
			culture.DateTimeFormat.ShortDatePattern = m_sDATEFORMAT;
			culture.DateTimeFormat.ShortTimePattern = m_sTIMEFORMAT;

			// 03/30/2007 Paul.  Always set the currency symbol.  It is not retained between page requests. 
			// 07/28/2006 Paul.  We cannot set the CurrencySymbol directly on Mono as it is read-only.  
			// 03/07/2008 Paul.  Move all localization to InitializeCulture(). 
			// Just clone the culture and modify the clone. 
			// CultureInfo culture = Thread.CurrentThread.CurrentCulture.Clone() as CultureInfo;
			culture.NumberFormat.CurrencySymbol   = C10n.SYMBOL;

			// 05/09/2006 Paul.  Initialize the numeric separators. 
			// 03/07/2008 Paul.  We are going to stop allowing the user to set the number separators. 
			// It does not work well when we allow the user to change the language using a drop-down. 
			// We are now using Microsoft AJAX localization, so there is no longer a need to manage the localization ourselves. 

			//string sGROUP_SEPARATOR   = Sql.ToString(Session["USER_SETTINGS/GROUP_SEPARATOR"  ]);
			//string sDECIMAL_SEPARATOR = Sql.ToString(Session["USER_SETTINGS/DECIMAL_SEPARATOR"]);
			// 06/03/2006 Paul.  Setting the separators is causing some users a problem.  It may be because the strings were empty. 
			// 02/29/2008 Paul.  The config value should only be used as an override.  We should default to the .NET culture value. 
			//if ( !Sql.IsEmptyString(sGROUP_SEPARATOR  ) ) culture.NumberFormat.CurrencyGroupSeparator   = sGROUP_SEPARATOR  ;
			//if ( !Sql.IsEmptyString(sDECIMAL_SEPARATOR) ) culture.NumberFormat.CurrencyDecimalSeparator = sDECIMAL_SEPARATOR;
			//if ( !Sql.IsEmptyString(sGROUP_SEPARATOR  ) ) culture.NumberFormat.NumberGroupSeparator     = sGROUP_SEPARATOR  ;
			//if ( !Sql.IsEmptyString(sDECIMAL_SEPARATOR) ) culture.NumberFormat.NumberDecimalSeparator   = sDECIMAL_SEPARATOR;

			// 08/30/2005 Paul. We don't need the long time pattern because we simply do not use it. 
			//Thread.CurrentThread.CurrentCulture.DateTimeFormat.LongTimePattern  = m_sTIMEFORMAT;
			//Thread.CurrentThread.CurrentUICulture = Thread.CurrentThread.CurrentCulture;
			//08/05/2006 Paul.  Apply the modified cultures. 
			Thread.CurrentThread.CurrentCulture   = culture;
			Thread.CurrentThread.CurrentUICulture = culture;
		}

		public L10N GetL10n()
		{
			return L10n;
		}

		public TimeZone GetT10n()
		{
			return T10n;
		}

		public Currency GetC10n()
		{
			return C10n;
		}

		// 11/19/2005 Paul.  Default to expiring everything. 
		virtual protected bool AuthenticationRequired()
		{
			return true;
		}

		virtual protected bool AdminPage()
		{
			return false;
		}

		public bool IsMobile
		{
			get
			{
				return (this.Theme == "Mobile");
			}
		}
        protected void AppendHomeList(PlaceHolder plc)
        {
            List<string> lstUrl = new List<string>();
            // Using parameterized query to prevent SQL injection
            string sql = @"SELECT distinct c.RESERVE1 FROM dbo.USERS a
                                         LEFT JOIN dbo.ACL_ROLES_USERS b ON a.ID=b.USER_ID AND b.DELETED=0
                                         LEFT JOIN dbo.ER_WORKFLOW c ON b.ROLE_ID=c.ROLE_ID AND c.DELETED=0
                                         WHERE a.ID=@USER_ID AND a.DELETED=0";
            System.Collections.Generic.Dictionary<string, object> parameters = new System.Collections.Generic.Dictionary<string, object>();
            parameters.Add("@USER_ID", Security.USER_ID);
            DataTable dt = APSqlProcs.GetData(sql, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                if (dt.Select("RESERVE1=1").Length > 0 || string.IsNullOrEmpty(dt.Rows[0][0].ToString()))
                    lstUrl.Add(this.ResolveUrl("../ER_REPORT_HEADER/MyReport.ascx"));
                lstUrl.Add(this.ResolveUrl("../ER_REPORT_HEADER/MyClosedReport.ascx"));
                lstUrl.Add(this.ResolveUrl("../ER_REPORT_HEADER/OtherReport.ascx"));
                lstUrl.Add(this.ResolveUrl("../ER_REPORT_HEADER/MyApprovedReport.ascx"));                
                foreach (string strUrl in lstUrl)
                {
                    try
                    {
                        UpdatePanel pnl = new UpdatePanel();
                        plc.Controls.Add(pnl);
                        Control ctl = LoadControl(strUrl);
                        pnl.ContentTemplateContainer.Controls.Add(ctl);
                        //}
                    }
                    catch (Exception ex)
                    {
                        Label lblError = new Label();
                        lblError.ID = "lblDetailViewRelationshipsError";
                        lblError.Text = Utils.ExpandException(ex);
                        lblError.ForeColor = System.Drawing.Color.Red;
                        lblError.EnableViewState = false;
                        plc.Controls.Add(lblError);
                    }
                }
            }

        }
        protected void AppendExpenseList(PlaceHolder plc)
        {
            List<string> lstUrl = new List<string>();
            // Using parameterized query to prevent SQL injection
            string sql = @"SELECT distinct c.RESERVE1 FROM dbo.USERS a
                                         LEFT JOIN dbo.ACL_ROLES_USERS b ON a.ID=b.USER_ID AND b.DELETED=0
                                         LEFT JOIN dbo.ER_WORKFLOW c ON b.ROLE_ID=c.ROLE_ID AND c.DELETED=0
                                         WHERE a.ID=@USER_ID AND a.DELETED=0 AND ISNULL(c.DELETED,0)=0";
            System.Collections.Generic.Dictionary<string, object> parameters = new System.Collections.Generic.Dictionary<string, object>();
            parameters.Add("@USER_ID", Security.USER_ID);
            DataTable dt = APSqlProcs.GetData(sql, parameters);
            if (dt != null && dt.Rows.Count > 0)
            {
                if (dt.Select("RESERVE1=1").Length > 0 || string.IsNullOrEmpty(dt.Rows[0][0].ToString()))
                {
                    lstUrl.Add(this.ResolveUrl("ListView.ascx"));
                }
                else
                {
                    lstUrl.Add(this.ResolveUrl("ListViewComplete.ascx"));
                    //lstUrl.Add(this.ResolveUrl("ListViewAll.ascx"));
                }
                foreach (string strUrl in lstUrl)
                {
                    try
                    {
                        //UpdatePanel pnl = new UpdatePanel();

                        Control ctl = LoadControl(strUrl);
                        //pnl.ContentTemplateContainer.Controls.Add(ctl);
                        plc.Controls.Add(ctl);
                        //}
                    }
                    catch (Exception ex)
                    {
                        Label lblError = new Label();
                        lblError.ID = "lblDetailViewRelationshipsError";
                        lblError.Text = Utils.ExpandException(ex);
                        lblError.ForeColor = System.Drawing.Color.Red;
                        lblError.EnableViewState = false;
                        plc.Controls.Add(lblError);
                    }
                }
            }
        }
		protected void AppendDetailViewRelationships(string sDETAIL_NAME, PlaceHolder plc)
		{
			// 11/17/2007 Paul.  Convert all view requests to a mobile request if appropriate.
			sDETAIL_NAME = sDETAIL_NAME + (this.IsMobile ? ".Mobile" : "");
			//int nPlatform = (int) Environment.OSVersion.Platform;
			DataTable dtFields = SplendidCache.DetailViewRelationships(sDETAIL_NAME);
			foreach(DataRow row in dtFields.Rows)
			{
				string sMODULE_NAME  = Sql.ToString(row["MODULE_NAME" ]);
				string sCONTROL_NAME = Sql.ToString(row["CONTROL_NAME"]);
				// 04/27/2006 Paul.  Only add the control if the user has access. 
				if ( Security.GetUserAccess(sMODULE_NAME, "list") >= 0 )
				{
					try
					{
						// 09/21/2008 Paul.  Mono does not fully support AJAX at this time. 
						// 09/22/2008 Paul.  The UpdatePanel is no longer crashing Mono, so resume using it. 
						//if ( nPlatform == 4 || nPlatform == 128 )
						//{
						//	Control ctl = LoadControl(sCONTROL_NAME + ".ascx");
						//	plc.Controls.Add(ctl);
						//}
						//else
						//{
							// 05/02/2008 Paul.  Put an update panel around all sub panels. This will allow in-place pagination and sorting. 
							UpdatePanel pnl = new UpdatePanel();
							plc.Controls.Add(pnl);
							Control ctl = LoadControl(sCONTROL_NAME + ".ascx");
							pnl.ContentTemplateContainer.Controls.Add(ctl);
						//}
					}
					catch(Exception ex)
					{
						Label lblError = new Label();
						// 06/09/2006 Paul.  Catch the error and display a message instead of crashing. 
						lblError.ID              = "lblDetailViewRelationshipsError";
						lblError.Text            = Utils.ExpandException(ex);
						lblError.ForeColor       = System.Drawing.Color.Red;
						lblError.EnableViewState = false;
						plc.Controls.Add(lblError);
					}
				}
			}
		}

		protected void AppendGridColumns(SplendidGrid grd, string sGRID_NAME)
		{
			AppendGridColumns(grd, sGRID_NAME, null);
		}

		// 02/08/2008 Paul.  We need to build a list of the fields used by the search clause. 
		protected void AppendGridColumns(SplendidGrid grd, string sGRID_NAME, UniqueStringCollection arrSelectFields)
		{
			// 11/17/2007 Paul.  Convert all view requests to a mobile request if appropriate.
			sGRID_NAME = sGRID_NAME + (this.IsMobile ? ".Mobile" : "");
			grd.AppendGridColumns(sGRID_NAME, arrSelectFields);
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			if ( AdminPage() )
			{
				// 02/13/2007 Paul.  The Shortcuts are in the master page. 
				Utils.AdminShortcuts(this, true);
			}
			if ( !IsPostBack )
			{
				Page.DataBind();
			}
		}

		protected override void OnInit(EventArgs e)
		{
			this.Load += new System.EventHandler(this.Page_Load);
			if ( Request["PrecompileOnly"] == "1" )
				Response.End();
			if ( Sql.IsEmptyString(Application["imageURL"]) )
			{
				SplendidInit.InitSession();
			}
			if ( AuthenticationRequired() )
			{
				// 11/17/2007 Paul.  New function to determine if user is authenticated. 
				if ( !Security.IsAuthenticated() )
				{
                    string sUserName = Sql.ToString(Request["USER_NAME"]);
                    string sRomoteToken = Sql.ToString(Request["TOKEN"]);

                    if (!Sql.IsEmptyString(sUserName) && !Sql.IsEmptyString(sRomoteToken))
                    {
                        string sLocalToken = Utils.AppSettings["ap_token"];
                        if (!Sql.IsEmptyString(sLocalToken) && sRomoteToken.Trim().ToLower() == sLocalToken.Trim().ToLower())
                        {
                            bool bValidUser = false;
                            int iErrorCode = 0;
                            bValidUser = SplendidInit.LoginUser(Guid.Empty,sUserName, string.Empty, string.Empty, string.Empty, true, ref iErrorCode, Sql.ToBoolean(Security.USER_IS_SSO_LOGIN));
                            if (bValidUser)
                            {
                                //TODO: change defualt page logic, use user default module. allen 03/18/2009
                                //start
                                string sDefaultModule;
                                if (!Sql.IsEmptyString(Security.DEFAULT_MODULE))
                                {
                                    sDefaultModule = Security.DEFAULT_MODULE;
                                }
                                else
                                {
                                    sDefaultModule = Sql.ToString(Application["CONFIG.default_module"]);
                                }
                                //end
                                // 05/22/2008 Paul.  Check for redirect.  
                                string sRedirect = Sql.ToString(Request["Redirect"]);
                                // 05/22/2008 Paul.  Only allow virtual relative paths. 
                                if (sRedirect.StartsWith("~/"))
                                    Response.Redirect(sRedirect);
                                // 10/06/2007 Paul.  Prompt the user for the timezone. 
                                else if (Sql.IsEmptyString(Session["USER_SETTINGS/TIMEZONE/ORIGINAL"]))
                                    Response.Redirect("~/Users/<USER>");
                                else if (sDefaultModule.StartsWith("~/"))
                                    Response.Redirect(sDefaultModule);
                                else if (!Sql.IsEmptyString(sDefaultModule))
                                    Response.Redirect("~/" + sDefaultModule + "/");
                                else
                                    Response.Redirect("~/Home/");
                            }
                            else
                            {
                                Response.Redirect(string.Format("{0}?error_code={1}", Utils.AppSettings["ap_remote_login_url"], iErrorCode));
                            }
                        }
                        else
                        {
                            Response.Redirect(string.Format("{0}?error_code=2", Utils.AppSettings["ap_remote_login_url"]));
                        }
                    }
                    else
                    {
                        Response.Redirect("~/Users/<USER>" + Server.UrlEncode(Page.AppRelativeVirtualPath + Request.Url.Query));
                    }
				}
			}
			// 11/27/2006 Paul.  We want to show the SQL on the Demo sites, so add a config variable to allow it. 
			bDebug = Sql.ToBoolean(Application["CONFIG.show_sql"]);
#if DEBUG
			bDebug = true;
#endif
			
			// 08/30/2005 Paul.  Apply the new culture at the page level so that it is only applied once. 
			// 03/11/2008 Paul.  GetL10n was getting called twice. No real harm, just not ideal. 
			// 04/30/2006 Paul.  Use the Context to store pointers to the localization objects.
			// This is so that we don't need to require that the page inherits from SplendidPage. 
			// A port to DNN prompted this approach. 
			Context.Items["L10n"] = GetL10n();
			Context.Items["T10n"] = GetT10n();
			Context.Items["C10n"] = GetC10n();
			Context.Items["PrintView"] = m_bPrintView;
			base.OnInit(e);
		}
		
		protected void Page_PreInit(object sender, EventArgs e)
		{
			//if ( Request["PrintView"] == "true" )
			//	this.MasterPageFile = "~/PrintView.master";
			string sTheme = Sql.ToString(Session["USER_SETTINGS/THEME"]);
			if ( String.IsNullOrEmpty(sTheme) )
			{
				// 07/01/2008 Paul.  Check default theme.  The default will fall-back to Sugar. 
				sTheme = SplendidDefaults.Theme();
			}
			this.Theme = sTheme;
			if ( !String.IsNullOrEmpty(this.MasterPageFile) )
			{
				if ( !this.MasterPageFile.Contains("/App_MasterPages/") )
				{
					string sFileName = System.IO.Path.GetFileName(this.MasterPageFile);
					this.MasterPageFile = "~/App_MasterPages/" + sTheme + "/" + sFileName;
				}
			}



		}
	}

}
